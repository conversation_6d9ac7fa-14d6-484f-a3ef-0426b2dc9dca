/**
 * Authentication API Routes
 * Handles login, logout, register, and session management
 */

import { NextRequest } from 'next/server'
import { AuthService } from '@/core/kilatservice/auth.service'

const authService = new AuthService()

export async function POST(request: NextRequest) {
  try {
    const { pathname } = new URL(request.url)
    const body = await request.json()
    
    // Login endpoint
    if (pathname.endsWith('/login')) {
      const { email, password } = body
      
      if (!email || !password) {
        return Response.json(
          { error: 'Email and password are required' },
          { status: 400 }
        )
      }
      
      const result = await authService.login(email, password)
      
      if (!result.success) {
        return Response.json(
          { error: result.error },
          { status: 401 }
        )
      }
      
      return Response.json({
        success: true,
        user: result.user,
        token: result.token
      })
    }
    
    // Register endpoint
    if (pathname.endsWith('/register')) {
      const { email, password, name } = body
      
      if (!email || !password || !name) {
        return Response.json(
          { error: 'Email, password, and name are required' },
          { status: 400 }
        )
      }
      
      const result = await authService.register({ email, password, name })
      
      if (!result.success) {
        return Response.json(
          { error: result.error },
          { status: 400 }
        )
      }
      
      return Response.json({
        success: true,
        user: result.user,
        token: result.token
      })
    }
    
    // Logout endpoint
    if (pathname.endsWith('/logout')) {
      const token = request.headers.get('authorization')?.replace('Bearer ', '')
      
      if (token) {
        await authService.logout(token)
      }
      
      return Response.json({ success: true })
    }
    
    return Response.json(
      { error: 'Endpoint not found' },
      { status: 404 }
    )
    
  } catch (error) {
    console.error('Auth API Error:', error)
    return Response.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function GET(request: NextRequest) {
  try {
    const { pathname } = new URL(request.url)
    
    // Get current user session
    if (pathname.endsWith('/me')) {
      const token = request.headers.get('authorization')?.replace('Bearer ', '')
      
      if (!token) {
        return Response.json(
          { error: 'No token provided' },
          { status: 401 }
        )
      }
      
      const user = await authService.getCurrentUser(token)
      
      if (!user) {
        return Response.json(
          { error: 'Invalid token' },
          { status: 401 }
        )
      }
      
      return Response.json({ user })
    }
    
    return Response.json(
      { error: 'Endpoint not found' },
      { status: 404 }
    )
    
  } catch (error) {
    console.error('Auth API Error:', error)
    return Response.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
