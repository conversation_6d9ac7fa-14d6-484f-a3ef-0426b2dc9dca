/**
 * UsersService - Business logic for users
 * Handles CRUD operations and business rules
 */

import { KilatORM } from '@/core/kilatorm'
import { generateId } from '@/core/kilatlib/utils'

interface Users {
  id: string
  name: string
  createdAt: Date
  updatedAt: Date
}

interface CreateUsersData {
  name: string
}

interface UpdateUsersData {
  name?: string
}

interface GetAllOptions {
  page: number
  limit: number
  search?: string
}

interface GetAllResult {
  items: Users[]
  total: number
}

interface ServiceResult<T = any> {
  success: boolean
  data?: T
  error?: string
}

export class UsersService {
  private orm: KilatORM
  
  constructor() {
    this.orm = new KilatORM()
  }
  
  /**
   * Get all users with pagination
   */
  async getAll(options: GetAllOptions): Promise<GetAllResult> {
    try {
      const { page, limit, search } = options
      const offset = (page - 1) * limit
      
      let query: any = {}
      
      if (search) {
        query = {
          name: { $regex: search, $options: 'i' }
        }
      }
      
      const [items, total] = await Promise.all([
        this.orm.users.find(query)
          .skip(offset)
          .limit(limit)
          .sort({ createdAt: -1 }),
        this.orm.users.countDocuments(query)
      ])
      
      return { items, total }
      
    } catch (error) {
      console.error('Get all users error:', error)
      return { items: [], total: 0 }
    }
  }
  
  /**
   * Get users by ID
   */
  async getById(id: string): Promise<Users | null> {
    try {
      return await this.orm.users.findById(id)
    } catch (error) {
      console.error('Get users by ID error:', error)
      return null
    }
  }
  
  /**
   * Create new users
   */
  async create(data: CreateUsersData): Promise<ServiceResult> {
    try {
      const users = await this.orm.users.create({
        id: generateId('users'),
        ...data,
        createdAt: new Date(),
        updatedAt: new Date()
      })
      
      return {
        success: true,
        data: users
      }
      
    } catch (error) {
      console.error('Create users error:', error)
      return {
        success: false,
        error: 'Failed to create users'
      }
    }
  }
  
  /**
   * Update users
   */
  async update(id: string, data: UpdateUsersData): Promise<ServiceResult> {
    try {
      const existing = await this.orm.users.findById(id)
      
      if (!existing) {
        return {
          success: false,
          error: 'Users not found'
        }
      }
      
      const updated = await this.orm.users.updateOne(
        { id },
        {
          ...data,
          updatedAt: new Date()
        }
      )
      
      return {
        success: true,
        data: updated
      }
      
    } catch (error) {
      console.error('Update users error:', error)
      return {
        success: false,
        error: 'Failed to update users'
      }
    }
  }
  
  /**
   * Delete users
   */
  async delete(id: string): Promise<ServiceResult> {
    try {
      const existing = await this.orm.users.findById(id)
      
      if (!existing) {
        return {
          success: false,
          error: 'Users not found'
        }
      }
      
      await this.orm.users.deleteOne({ id })
      
      return {
        success: true
      }
      
    } catch (error) {
      console.error('Delete users error:', error)
      return {
        success: false,
        error: 'Failed to delete users'
      }
    }
  }
}
