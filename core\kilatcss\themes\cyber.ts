/**
 * Cyber Theme - Futuristic cyberpunk theme for Kilat.js
 * High contrast neon colors with dark background
 */

export const cyberTheme = {
  name: 'cyber',
  displayName: 'Cyber',
  type: 'dark',
  
  colors: {
    // Primary colors (Neon Green)
    primary: {
      50: '#f0fff4',
      100: '#dcfce7',
      200: '#bbf7d0',
      300: '#86efac',
      400: '#4ade80',
      500: '#00ff88', // Main primary - Neon Green
      600: '#16a34a',
      700: '#15803d',
      800: '#166534',
      900: '#14532d',
      950: '#052e16',
    },
    
    // Secondary colors (Neon Pink)
    secondary: {
      50: '#fdf2f8',
      100: '#fce7f3',
      200: '#fbcfe8',
      300: '#f9a8d4',
      400: '#f472b6',
      500: '#ff0080', // Main secondary - Neon Pink
      600: '#db2777',
      700: '#be185d',
      800: '#9d174d',
      900: '#831843',
      950: '#500724',
    },
    
    // Accent colors (Neon Blue)
    accent: {
      50: '#f0f9ff',
      100: '#e0f2fe',
      200: '#bae6fd',
      300: '#7dd3fc',
      400: '#38bdf8',
      500: '#00d4ff', // Main accent - Neon Blue
      600: '#0284c7',
      700: '#0369a1',
      800: '#075985',
      900: '#0c4a6e',
      950: '#082f49',
    },
    
    // Neutral colors (Dark)
    neutral: {
      50: '#f8fafc',
      100: '#f1f5f9',
      200: '#e2e8f0',
      300: '#cbd5e1',
      400: '#94a3b8',
      500: '#64748b',
      600: '#475569',
      700: '#334155',
      800: '#1a1a1a', // Surface
      900: '#0a0a0a', // Background
      950: '#000000',
    },
    
    // Semantic colors
    success: {
      500: '#00ff88',
    },
    warning: {
      500: '#ffff00',
    },
    error: {
      500: '#ff0080',
    },
  },
  
  // CSS custom properties
  cssVars: {
    '--kilat-primary': '#00ff88',
    '--kilat-secondary': '#ff0080',
    '--kilat-accent': '#00d4ff',
    '--kilat-background': '#0a0a0a',
    '--kilat-surface': '#1a1a1a',
    '--kilat-text': '#ffffff',
    '--kilat-muted': '#666666',
    '--kilat-border': '#333333',
    '--kilat-success': '#00ff88',
    '--kilat-warning': '#ffff00',
    '--kilat-error': '#ff0080',
  },
  
  // Shadows and effects
  shadows: {
    glow: '0 0 20px rgba(0, 255, 136, 0.8)',
    'glow-lg': '0 0 40px rgba(0, 255, 136, 0.9)',
    'glow-xl': '0 0 60px rgba(0, 255, 136, 1)',
    'pink-glow': '0 0 20px rgba(255, 0, 128, 0.8)',
    'blue-glow': '0 0 20px rgba(0, 212, 255, 0.8)',
    neon: '0 0 5px currentColor, 0 0 10px currentColor, 0 0 15px currentColor',
  },
  
  // Gradients
  gradients: {
    primary: 'linear-gradient(45deg, #00ff88, #00d4ff)',
    secondary: 'linear-gradient(45deg, #ff0080, #00ff88)',
    accent: 'linear-gradient(45deg, #00d4ff, #ff0080)',
    hero: 'linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 50%, #000000 100%)',
    neon: 'linear-gradient(90deg, #00ff88, #00d4ff, #ff0080, #00ff88)',
  },
  
  // Typography
  typography: {
    fontFamily: {
      sans: ['Orbitron', 'system-ui', 'sans-serif'],
      mono: ['Fira Code', 'Consolas', 'monospace'],
      display: ['Exo 2', 'system-ui', 'sans-serif'],
    },
  },
  
  // Animations
  animations: {
    'neon-pulse': 'neonPulse 2s ease-in-out infinite alternate',
    'glitch': 'glitch 0.3s ease-in-out infinite',
    'scan-line': 'scanLine 2s linear infinite',
    'matrix': 'matrix 20s linear infinite',
    'cyber-glow': 'cyberGlow 3s ease-in-out infinite alternate',
  },
  
  // Component styles
  components: {
    button: {
      primary: {
        background: 'transparent',
        color: '#00ff88',
        border: '2px solid #00ff88',
        textShadow: '0 0 10px #00ff88',
        boxShadow: '0 0 20px rgba(0, 255, 136, 0.5)',
        '&:hover': {
          background: 'rgba(0, 255, 136, 0.1)',
          boxShadow: '0 0 30px rgba(0, 255, 136, 0.8)',
        },
      },
      secondary: {
        background: 'transparent',
        color: '#ff0080',
        border: '2px solid #ff0080',
        textShadow: '0 0 10px #ff0080',
        boxShadow: '0 0 20px rgba(255, 0, 128, 0.5)',
        '&:hover': {
          background: 'rgba(255, 0, 128, 0.1)',
          boxShadow: '0 0 30px rgba(255, 0, 128, 0.8)',
        },
      },
    },
    
    card: {
      default: {
        background: 'rgba(26, 26, 26, 0.8)',
        border: '1px solid #333333',
        borderRadius: '0.25rem',
        boxShadow: '0 0 20px rgba(0, 255, 136, 0.1)',
      },
      neon: {
        background: 'rgba(26, 26, 26, 0.9)',
        border: '2px solid #00ff88',
        borderRadius: '0.25rem',
        boxShadow: '0 0 30px rgba(0, 255, 136, 0.3)',
      },
    },
  },
}
