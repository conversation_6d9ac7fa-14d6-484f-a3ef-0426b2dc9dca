import { Ki<PERSON><PERSON><PERSON> } from '../components/ui/card' import { <PERSON><PERSON><PERSON>utt<PERSON> } from '../components/ui/button' export default function DashboardUsersPage() { const users = [ { id: '1', name: '<PERSON>', email: '<EMAIL>', role: 'admin', status: 'active', lastLogin: '2025-01-09T10:30:00Z' }, { id: '2', name: '<PERSON>', email: '<EMAIL>', role: 'user', status: 'active', lastLogin: '2025-01-08T15:45:00Z' }, { id: '3', name: '<PERSON>', email: '<EMAIL>', role: 'user', status: 'inactive', lastLogin: '2025-01-05T09:15:00Z' }, ] return ( React.createElement('div', null, <div className="flex items-center justify-between animate-kilat-fade-in"> <div> <h1 className="text-3xl font-bold mb-2">Users Management</h1> <p className="text-glow-muted">Manage user accounts and permissions</p> ) React.createElement('Ki<PERSON>Button', null, Add New User ) </div> {} React.createElement('div', null, <KilatCard className="animate-kilat-slide-up"> <div className="p-6"> <div className="flex items-center justify-between"> <div> <p className="text-sm text-glow-muted">Total Users</p> <p className="text-2xl font-bold text-glow-primary">{users.length}</p> ) React.createElement('div', null, 👥 ) </div> </div> </KilatCard> React.createElement('KilatCard', null, <div className="p-6"> <div className="flex items-center justify-between"> <div> <p className="text-sm text-glow-muted">Active Users</p> <p className="text-2xl font-bold text-glow-secondary"> {users.filter(u => u.status === 'active').length} </p> </div> <div className="w-12 h-12 bg-glow-secondary/20 rounded-lg flex items-center justify-center"> ✅ </div> </div> </div> ) React.createElement('KilatCard', null, <div className="p-6"> <div className="flex items-center justify-between"> <div> <p className="text-sm text-glow-muted">Admins</p> <p className="text-2xl font-bold text-glow-accent"> {users.filter(u => u.role === 'admin').length} </p> </div> <div className="w-12 h-12 bg-glow-accent/20 rounded-lg flex items-center justify-center"> 👑 </div> </div> </div> ) React.createElement('KilatCard', null, <div className="p-6"> <div className="flex items-center justify-between"> <div> <p className="text-sm text-glow-muted">New This Week</p> <p className="text-2xl font-bold text-green-400">2</p> </div> <div className="w-12 h-12 bg-green-400/20 rounded-lg flex items-center justify-center"> 📈 </div> </div> </div> ) </div> {} React.createElement('KilatCard', null, <div className="p-6"> <div className="flex items-center justify-between mb-6"> <h2 className="text-xl font-semibold">All Users</h2> <div className="flex items-center space-x-4"> <input type="text" placeholder="Search users..." className="px-4 py-2 bg-glow-surface border border-glow-surface rounded-lg focus:outline-none focus:ring-2 focus:ring-glow-primary/50" /> <select className="px-4 py-2 bg-glow-surface border border-glow-surface rounded-lg focus:outline-none focus:ring-2 focus:ring-glow-primary/50"> <option value="">All Roles</option> <option value="admin">Admin</option> <option value="user">User</option> </select> </div> </div> <div className="overflow-x-auto"> <table className="w-full"> <thead> <tr className="border-b border-glow-surface"> <th className="text-left py-3 px-4 font-medium text-glow-muted">User</th> <th className="text-left py-3 px-4 font-medium text-glow-muted">Role</th> <th className="text-left py-3 px-4 font-medium text-glow-muted">Status</th> <th className="text-left py-3 px-4 font-medium text-glow-muted">Last Login</th> <th className="text-left py-3 px-4 font-medium text-glow-muted">Actions</th> </tr> </thead> <tbody> {users.map((user, index) => ( <tr key={user.id} className="border-b border-glow-surface/50 hover:bg-glow-surface/30 transition-colors" style={{ animationDelay: `${index * 0.1}s` }} > <td className="py-4 px-4"> <div className="flex items-center space-x-3"> <div className="w-10 h-10 bg-glow-primary rounded-full flex items-center justify-center text-white font-semibold"> {user.name.charAt(0)} </div> <div> <p className="font-medium">{user.name}</p> <p className="text-sm text-glow-muted">{user.email}</p> </div> </div> </td> <td className="py-4 px-4"> <span className={`px-2 py-1 rounded-full text-xs font-medium ${ user.role === 'admin' ? 'bg-glow-accent/20 text-glow-accent' : 'bg-glow-secondary/20 text-glow-secondary' }`}> {user.role} </span> </td> <td className="py-4 px-4"> <span className={`px-2 py-1 rounded-full text-xs font-medium ${ user.status === 'active' ? 'bg-green-500/20 text-green-400' : 'bg-red-500/20 text-red-400' }`}> {user.status} </span> </td> <td className="py-4 px-4 text-sm text-glow-muted"> {new Date(user.lastLogin).toLocaleDateString()} </td> <td className="py-4 px-4"> <div className="flex items-center space-x-2"> <button className="p-2 hover:bg-glow-surface rounded-lg transition-colors"> ✏️ </button> <button className="p-2 hover:bg-glow-surface rounded-lg transition-colors"> 🗑️ </button> <button className="p-2 hover:bg-glow-surface rounded-lg transition-colors"> 👁️ </button> </div> </td> </tr> ))} </tbody> </table> </div> {} <div className="flex items-center justify-between mt-6"> <p className="text-sm text-glow-muted"> Showing 1 to {users.length} of {users.length} users </p> <div className="flex items-center space-x-2"> <KilatButton variant="outline" size="sm" disabled> Previous </KilatButton> <KilatButton variant="primary" size="sm"> 1 </KilatButton> <KilatButton variant="outline" size="sm" disabled> Next </KilatButton> </div> </div> </div> ) </div> ) }
//# sourceMappingURL=apps\dashboard\users\page.tsx.map