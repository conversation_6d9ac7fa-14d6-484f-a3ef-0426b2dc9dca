#!/usr/bin/env node

/**
 * Kilat.js Demo Server - Simple server to showcase the beautiful homepage
 */

const { createServer } = require('http')

const PORT = 3001

const HTML_CONTENT = `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Kilat.js - Lightning Fast Fullstack Framework</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <style>
    @keyframes kilat-fade-in {
      from { opacity: 0; transform: translateY(10px); }
      to { opacity: 1; transform: translateY(0); }
    }
    @keyframes kilat-glow {
      from { box-shadow: 0 0 10px rgba(96, 165, 250, 0.5); }
      to { box-shadow: 0 0 30px rgba(96, 165, 250, 0.5), 0 0 60px rgba(96, 165, 250, 0.3); }
    }
    @keyframes kilat-float {
      0%, 100% { transform: translateY(0px); }
      50% { transform: translateY(-20px); }
    }
    @keyframes kilat-pulse {
      0%, 100% { transform: scale(1); opacity: 1; }
      50% { transform: scale(1.05); opacity: 0.8; }
    }
    @keyframes kilat-gradient {
      0% { background-position: 0% 50%; }
      50% { background-position: 100% 50%; }
      100% { background-position: 0% 50%; }
    }
    .animate-kilat-fade-in { animation: kilat-fade-in 0.8s ease-out; }
    .animate-kilat-glow { animation: kilat-glow 3s ease-in-out infinite alternate; }
    .animate-kilat-float { animation: kilat-float 6s ease-in-out infinite; }
    .animate-kilat-pulse { animation: kilat-pulse 2s ease-in-out infinite; }
    .animate-kilat-gradient { animation: kilat-gradient 4s ease-in-out infinite; }
    .animate-kilat-scale-in { animation: kilat-fade-in 0.6s ease-out; }
    .animate-kilat-slide-up { animation: kilat-fade-in 0.8s ease-out; }
    .bg-300 { background-size: 300% 300%; }
  </style>
</head>
<body>
  <div class="min-h-screen bg-gradient-to-br from-slate-900 via-blue-900 to-slate-900 text-white overflow-hidden">
    <!-- Hero Section -->
    <section class="relative py-20 px-4 min-h-screen flex items-center">
      <div class="container mx-auto text-center relative z-10">
        <div class="animate-kilat-fade-in">
          <!-- Logo/Brand -->
          <div class="mb-8">
            <div class="inline-flex items-center justify-center w-24 h-24 mb-6 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 animate-kilat-glow">
              <span class="text-4xl font-bold">⚡</span>
            </div>
          </div>
          
          <!-- Main Title -->
          <h1 class="text-7xl md:text-9xl font-black mb-6 bg-gradient-to-r from-blue-400 via-purple-500 to-cyan-400 bg-clip-text text-transparent animate-kilat-gradient bg-300 leading-tight">
            Kilat.js
          </h1>
          
          <!-- Subtitle -->
          <p class="text-xl md:text-3xl text-slate-300 mb-4 max-w-4xl mx-auto font-light leading-relaxed">
            The <span class="text-blue-400 font-semibold">lightning-fast</span> fullstack framework
          </p>
          <p class="text-lg md:text-xl text-slate-400 mb-12 max-w-3xl mx-auto">
            Built from scratch with <span class="text-purple-400">Bun.js</span>, 
            <span class="text-cyan-400"> file-based routing</span>, and 
            <span class="text-green-400"> zero dependencies</span>
          </p>
          
          <!-- CTA Buttons -->
          <div class="flex flex-col sm:flex-row gap-6 justify-center items-center mb-16">
            <button class="group relative px-8 py-4 bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl font-semibold text-lg transition-all duration-300 hover:scale-105 hover:shadow-2xl hover:shadow-blue-500/25 animate-kilat-scale-in">
              <span class="relative z-10">Get Started</span>
              <div class="absolute inset-0 bg-gradient-to-r from-blue-600 to-purple-700 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
            </button>
            
            <button class="group px-8 py-4 border-2 border-slate-600 rounded-xl font-semibold text-lg transition-all duration-300 hover:border-blue-400 hover:text-blue-400 hover:shadow-lg hover:shadow-blue-400/20 animate-kilat-scale-in" style="animation-delay: 0.2s">
              <span class="flex items-center gap-2">
                <span>View Documentation</span>
                <span class="group-hover:translate-x-1 transition-transform duration-300">→</span>
              </span>
            </button>
          </div>
          
          <!-- Quick Start Code -->
          <div class="max-w-2xl mx-auto animate-kilat-slide-up" style="animation-delay: 0.4s">
            <div class="bg-slate-800/50 backdrop-blur-sm border border-slate-700 rounded-2xl p-6 text-left">
              <div class="flex items-center gap-2 mb-4">
                <div class="w-3 h-3 bg-red-500 rounded-full"></div>
                <div class="w-3 h-3 bg-yellow-500 rounded-full"></div>
                <div class="w-3 h-3 bg-green-500 rounded-full"></div>
                <span class="text-slate-400 text-sm ml-2">Terminal</span>
              </div>
              <pre class="text-green-400 font-mono text-sm md:text-base">
<span class="text-slate-500"># Create new Kilat.js app</span>
<span class="text-blue-400">npm</span> create kilat-app my-app

<span class="text-slate-500"># Start development server</span>
<span class="text-purple-400">cd</span> my-app && <span class="text-cyan-400">bun</span> dev
              </pre>
            </div>
          </div>
        </div>
      </div>
      
      <!-- Animated Background -->
      <div class="absolute inset-0 overflow-hidden">
        <!-- Floating Orbs -->
        <div class="absolute top-1/4 left-1/4 w-96 h-96 bg-blue-500/10 rounded-full blur-3xl animate-kilat-float"></div>
        <div class="absolute bottom-1/4 right-1/4 w-80 h-80 bg-purple-500/10 rounded-full blur-3xl animate-kilat-float" style="animation-delay: 2s"></div>
        <div class="absolute top-3/4 left-1/2 w-64 h-64 bg-cyan-500/10 rounded-full blur-3xl animate-kilat-float" style="animation-delay: 4s"></div>
        
        <!-- Grid Pattern -->
        <div class="absolute inset-0 bg-[linear-gradient(rgba(255,255,255,0.02)_1px,transparent_1px),linear-gradient(90deg,rgba(255,255,255,0.02)_1px,transparent_1px)] bg-[size:50px_50px] [mask-image:radial-gradient(ellipse_80%_50%_at_50%_0%,#000_70%,transparent_110%)]"></div>
      </div>
    </section>

    <!-- Performance Stats -->
    <section class="py-32 px-4">
      <div class="container mx-auto">
        <div class="text-center mb-20">
          <h2 class="text-5xl md:text-6xl font-bold mb-6 bg-gradient-to-r from-white to-slate-300 bg-clip-text text-transparent animate-kilat-slide-up">
            Performance That Speaks
          </h2>
          <p class="text-xl text-slate-400 max-w-3xl mx-auto animate-kilat-slide-up" style="animation-delay: 0.2s">
            Numbers don't lie. See how Kilat.js outperforms traditional frameworks
          </p>
        </div>
        
        <div class="grid md:grid-cols-4 gap-8 max-w-5xl mx-auto">
          <div class="text-center animate-kilat-scale-in" style="animation-delay: 0.3s">
            <div class="text-5xl md:text-6xl font-black bg-gradient-to-r from-blue-400 to-cyan-400 bg-clip-text text-transparent mb-4 animate-kilat-pulse">
              &lt;10ms
            </div>
            <p class="text-slate-300 font-semibold mb-2">Cold Start</p>
            <p class="text-slate-500 text-sm">Lightning fast server startup</p>
          </div>
          
          <div class="text-center animate-kilat-scale-in" style="animation-delay: 0.4s">
            <div class="text-5xl md:text-6xl font-black bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent mb-4 animate-kilat-pulse" style="animation-delay: 0.5s">
              0.1s
            </div>
            <p class="text-slate-300 font-semibold mb-2">Hot Reload</p>
            <p class="text-slate-500 text-sm">Instant development feedback</p>
          </div>
          
          <div class="text-center animate-kilat-scale-in" style="animation-delay: 0.5s">
            <div class="text-5xl md:text-6xl font-black bg-gradient-to-r from-green-400 to-emerald-400 bg-clip-text text-transparent mb-4 animate-kilat-pulse" style="animation-delay: 1s">
              50KB
            </div>
            <p class="text-slate-300 font-semibold mb-2">Bundle Size</p>
            <p class="text-slate-500 text-sm">Minimal production footprint</p>
          </div>
          
          <div class="text-center animate-kilat-scale-in" style="animation-delay: 0.6s">
            <div class="text-5xl md:text-6xl font-black bg-gradient-to-r from-orange-400 to-red-400 bg-clip-text text-transparent mb-4 animate-kilat-pulse" style="animation-delay: 1.5s">
              100%
            </div>
            <p class="text-slate-300 font-semibold mb-2">TypeScript</p>
            <p class="text-slate-500 text-sm">Full type safety out of the box</p>
          </div>
        </div>
      </div>
    </section>

    <!-- Footer -->
    <footer class="py-20 px-4 border-t border-slate-800">
      <div class="container mx-auto text-center">
        <div class="mb-8">
          <div class="inline-flex items-center justify-center w-16 h-16 mb-4 rounded-full bg-gradient-to-r from-blue-500 to-purple-600">
            <span class="text-2xl font-bold">⚡</span>
          </div>
          <h3 class="text-2xl font-bold text-white mb-2">Kilat.js</h3>
          <p class="text-slate-400">Lightning-fast fullstack framework</p>
        </div>
        
        <div class="flex flex-wrap justify-center gap-8 mb-8 text-slate-400">
          <a href="#" class="hover:text-blue-400 transition-colors duration-300">Documentation</a>
          <a href="#" class="hover:text-blue-400 transition-colors duration-300">Examples</a>
          <a href="#" class="hover:text-blue-400 transition-colors duration-300">GitHub</a>
          <a href="#" class="hover:text-blue-400 transition-colors duration-300">Community</a>
          <a href="#" class="hover:text-blue-400 transition-colors duration-300">Blog</a>
        </div>
        
        <div class="text-slate-500 text-sm">
          <p>&copy; 2024 Kilat.js. Built with ⚡ and ❤️</p>
        </div>
      </div>
    </footer>
  </div>
</body>
</html>`

const server = createServer((req, res) => {
  const url = new URL(req.url || '/', `http://${req.headers.host}`)

  // Set CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*')
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization')

  // Serve the homepage
  if (url.pathname === '/' || url.pathname === '/index.html') {
    res.writeHead(200, {
      'Content-Type': 'text/html',
      'X-Powered-By': 'Kilat.js Demo Server'
    })
    res.end(HTML_CONTENT)
    return
  }

  // 404 for other routes
  res.writeHead(404, { 'Content-Type': 'text/plain' })
  res.end('404 - Not Found')
})

server.listen(PORT, () => {
  console.log(`⚡ Kilat.js Demo Server running at http://localhost:${PORT}`)
  console.log(`🎨 Beautiful homepage ready to view!`)
  console.log(`🚀 Open your browser and see the magic!`)
})
