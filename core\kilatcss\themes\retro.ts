/**
 * Retro Theme - Vintage 80s/90s theme for Kilat.js
 * Warm colors with retro aesthetics
 */

export const retroTheme = {
  name: 'retro',
  displayName: 'Retro',
  type: 'dark',
  
  colors: {
    // Primary colors (Retro Orange)
    primary: {
      50: '#fffbeb',
      100: '#fef3c7',
      200: '#fde68a',
      300: '#fcd34d',
      400: '#fbbf24',
      500: '#f59e0b', // Main primary
      600: '#d97706',
      700: '#b45309',
      800: '#92400e',
      900: '#78350f',
      950: '#451a03',
    },
    
    // Secondary colors (Retro Red)
    secondary: {
      50: '#fef2f2',
      100: '#fee2e2',
      200: '#fecaca',
      300: '#fca5a5',
      400: '#f87171',
      500: '#ef4444', // Main secondary
      600: '#dc2626',
      700: '#b91c1c',
      800: '#991b1b',
      900: '#7f1d1d',
      950: '#450a0a',
    },
    
    // Accent colors (Retro Green)
    accent: {
      50: '#ecfdf5',
      100: '#d1fae5',
      200: '#a7f3d0',
      300: '#6ee7b7',
      400: '#34d399',
      500: '#10b981', // Main accent
      600: '#059669',
      700: '#047857',
      800: '#065f46',
      900: '#064e3b',
      950: '#022c22',
    },
    
    // Neutral colors (Warm Dark)
    neutral: {
      50: '#fafaf9',
      100: '#f5f5f4',
      200: '#e7e5e4',
      300: '#d6d3d1',
      400: '#a8a29e',
      500: '#78716c', // Muted text
      600: '#57534e',
      700: '#44403c',
      800: '#292524', // Surface
      900: '#1c1917', // Background
      950: '#0c0a09',
    },
    
    // Semantic colors
    success: {
      500: '#10b981',
    },
    warning: {
      500: '#f59e0b',
    },
    error: {
      500: '#ef4444',
    },
  },
  
  // CSS custom properties
  cssVars: {
    '--kilat-primary': '#f59e0b',
    '--kilat-secondary': '#ef4444',
    '--kilat-accent': '#10b981',
    '--kilat-background': '#1c1917',
    '--kilat-surface': '#292524',
    '--kilat-text': '#fbbf24',
    '--kilat-muted': '#78716c',
    '--kilat-border': '#44403c',
    '--kilat-success': '#10b981',
    '--kilat-warning': '#f59e0b',
    '--kilat-error': '#ef4444',
  },
  
  // Shadows and effects
  shadows: {
    retro: '4px 4px 0px rgba(245, 158, 11, 0.8)',
    'retro-lg': '8px 8px 0px rgba(245, 158, 11, 0.8)',
    'retro-xl': '12px 12px 0px rgba(245, 158, 11, 0.8)',
    vintage: '0 0 20px rgba(245, 158, 11, 0.5)',
    'vintage-lg': '0 0 40px rgba(245, 158, 11, 0.6)',
  },
  
  // Gradients
  gradients: {
    primary: 'linear-gradient(45deg, #f59e0b, #ef4444)',
    secondary: 'linear-gradient(45deg, #ef4444, #10b981)',
    accent: 'linear-gradient(45deg, #10b981, #f59e0b)',
    hero: 'linear-gradient(135deg, #1c1917 0%, #292524 50%, #44403c 100%)',
    sunset: 'linear-gradient(90deg, #f59e0b, #ef4444, #dc2626)',
    vintage: 'linear-gradient(145deg, #292524, #1c1917)',
  },
  
  // Typography
  typography: {
    fontFamily: {
      sans: ['Courier New', 'monospace'],
      mono: ['Courier New', 'monospace'],
      display: ['Orbitron', 'monospace'],
    },
  },
  
  // Animations
  animations: {
    'retro-blink': 'retroBlink 1s step-end infinite',
    'vintage-glow': 'vintageGlow 2s ease-in-out infinite alternate',
    'scan-lines': 'scanLines 0.1s linear infinite',
    'crt-flicker': 'crtFlicker 0.15s linear infinite',
    'retro-bounce': 'retroBounce 2s ease-in-out infinite',
  },
  
  // Component styles
  components: {
    button: {
      primary: {
        background: 'var(--kilat-primary)',
        color: '#1c1917',
        border: '3px solid #1c1917',
        boxShadow: '4px 4px 0px #1c1917',
        fontWeight: 'bold',
        textTransform: 'uppercase',
        '&:hover': {
          transform: 'translate(2px, 2px)',
          boxShadow: '2px 2px 0px #1c1917',
        },
        '&:active': {
          transform: 'translate(4px, 4px)',
          boxShadow: 'none',
        },
      },
      secondary: {
        background: 'var(--kilat-secondary)',
        color: 'white',
        border: '3px solid #1c1917',
        boxShadow: '4px 4px 0px #1c1917',
        fontWeight: 'bold',
        textTransform: 'uppercase',
        '&:hover': {
          transform: 'translate(2px, 2px)',
          boxShadow: '2px 2px 0px #1c1917',
        },
      },
    },
    
    card: {
      default: {
        background: 'var(--kilat-surface)',
        border: '3px solid var(--kilat-primary)',
        borderRadius: '0px',
        boxShadow: '8px 8px 0px rgba(245, 158, 11, 0.3)',
      },
      vintage: {
        background: 'linear-gradient(145deg, #292524, #1c1917)',
        border: '2px solid #f59e0b',
        borderRadius: '0px',
        boxShadow: '0 0 20px rgba(245, 158, 11, 0.3)',
      },
      terminal: {
        background: '#000000',
        border: '2px solid #00ff00',
        borderRadius: '0px',
        fontFamily: 'Courier New, monospace',
        color: '#00ff00',
      },
    },
  },
}
