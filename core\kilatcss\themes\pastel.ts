/**
 * Pastel Theme - Light and soft theme for Kilat.js
 * Gentle pastel colors with light background
 */

export const pastelTheme = {
  name: 'pastel',
  displayName: 'Pastel',
  type: 'light',
  
  colors: {
    // Primary colors (Soft Blue)
    primary: {
      50: '#f0f9ff',
      100: '#e0f2fe',
      200: '#bae6fd',
      300: '#7dd3fc',
      400: '#38bdf8',
      500: '#0ea5e9', // Main primary
      600: '#0284c7',
      700: '#0369a1',
      800: '#075985',
      900: '#0c4a6e',
      950: '#082f49',
    },
    
    // Secondary colors (Soft Pink)
    secondary: {
      50: '#fdf2f8',
      100: '#fce7f3',
      200: '#fbcfe8',
      300: '#f9a8d4',
      400: '#f472b6',
      500: '#ec4899', // Main secondary
      600: '#db2777',
      700: '#be185d',
      800: '#9d174d',
      900: '#831843',
      950: '#500724',
    },
    
    // Accent colors (Soft Green)
    accent: {
      50: '#f0fdf4',
      100: '#dcfce7',
      200: '#bbf7d0',
      300: '#86efac',
      400: '#4ade80',
      500: '#22c55e', // Main accent
      600: '#16a34a',
      700: '#15803d',
      800: '#166534',
      900: '#14532d',
      950: '#052e16',
    },
    
    // Neutral colors (Light)
    neutral: {
      50: '#fefefe',
      100: '#fdfdfd',
      200: '#fafafa',
      300: '#f5f5f5',
      400: '#e5e5e5',
      500: '#a3a3a3', // Muted text
      600: '#737373',
      700: '#525252',
      800: '#404040',
      900: '#262626', // Text
      950: '#171717',
    },
    
    // Semantic colors
    success: {
      500: '#22c55e',
    },
    warning: {
      500: '#f59e0b',
    },
    error: {
      500: '#ef4444',
    },
  },
  
  // CSS custom properties
  cssVars: {
    '--kilat-primary': '#0ea5e9',
    '--kilat-secondary': '#ec4899',
    '--kilat-accent': '#22c55e',
    '--kilat-background': '#fefefe',
    '--kilat-surface': '#ffffff',
    '--kilat-text': '#262626',
    '--kilat-muted': '#a3a3a3',
    '--kilat-border': '#e5e5e5',
    '--kilat-success': '#22c55e',
    '--kilat-warning': '#f59e0b',
    '--kilat-error': '#ef4444',
  },
  
  // Shadows and effects
  shadows: {
    soft: '0 4px 20px rgba(0, 0, 0, 0.08)',
    'soft-lg': '0 8px 40px rgba(0, 0, 0, 0.12)',
    'soft-xl': '0 12px 60px rgba(0, 0, 0, 0.15)',
    colored: '0 4px 20px rgba(14, 165, 233, 0.15)',
    'colored-lg': '0 8px 40px rgba(14, 165, 233, 0.2)',
  },
  
  // Gradients
  gradients: {
    primary: 'linear-gradient(45deg, #0ea5e9, #ec4899)',
    secondary: 'linear-gradient(45deg, #ec4899, #22c55e)',
    accent: 'linear-gradient(45deg, #22c55e, #0ea5e9)',
    hero: 'linear-gradient(135deg, #fefefe 0%, #ffffff 50%, #fafafa 100%)',
    soft: 'linear-gradient(145deg, #ffffff, #fafafa)',
  },
  
  // Typography
  typography: {
    fontFamily: {
      sans: ['Inter', 'system-ui', 'sans-serif'],
      mono: ['JetBrains Mono', 'Consolas', 'monospace'],
      display: ['Poppins', 'system-ui', 'sans-serif'],
    },
  },
  
  // Animations
  animations: {
    'soft-bounce': 'softBounce 2s ease-in-out infinite',
    'gentle-float': 'gentleFloat 4s ease-in-out infinite',
    'soft-pulse': 'softPulse 3s ease-in-out infinite',
    'fade-in-up': 'fadeInUp 0.8s ease-out',
  },
  
  // Component styles
  components: {
    button: {
      primary: {
        background: 'var(--kilat-primary)',
        color: 'white',
        boxShadow: '0 4px 20px rgba(14, 165, 233, 0.3)',
        '&:hover': {
          background: 'rgba(14, 165, 233, 0.9)',
          transform: 'translateY(-2px)',
          boxShadow: '0 8px 30px rgba(14, 165, 233, 0.4)',
        },
      },
      secondary: {
        background: 'var(--kilat-secondary)',
        color: 'white',
        boxShadow: '0 4px 20px rgba(236, 72, 153, 0.3)',
        '&:hover': {
          background: 'rgba(236, 72, 153, 0.9)',
          transform: 'translateY(-2px)',
          boxShadow: '0 8px 30px rgba(236, 72, 153, 0.4)',
        },
      },
      outline: {
        border: '2px solid var(--kilat-primary)',
        color: 'var(--kilat-primary)',
        background: 'transparent',
        '&:hover': {
          background: 'rgba(14, 165, 233, 0.05)',
          transform: 'translateY(-1px)',
        },
      },
    },
    
    card: {
      default: {
        background: 'var(--kilat-surface)',
        border: '1px solid var(--kilat-border)',
        borderRadius: '1rem',
        boxShadow: 'var(--kilat-soft)',
      },
      elevated: {
        background: 'var(--kilat-surface)',
        border: 'none',
        borderRadius: '1rem',
        boxShadow: '0 8px 40px rgba(0, 0, 0, 0.12)',
      },
      colored: {
        background: 'linear-gradient(145deg, #ffffff, #f8fafc)',
        border: '1px solid rgba(14, 165, 233, 0.1)',
        borderRadius: '1rem',
        boxShadow: '0 4px 20px rgba(14, 165, 233, 0.1)',
      },
    },
  },
}
