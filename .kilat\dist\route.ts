import{NextRequest}from 'next/server' import{UsersService}from '@/core/kilatservice/users.service' const usersService=new UsersService()export async function GET(request: NextRequest){try{const{searchParams}=new URL(request.url)const page=parseInt(searchParams.get('page')||'1')const limit=parseInt(searchParams.get('limit')||'10')const result=await usersService.getAll({page,limit})return Response.json({success: true,data: result.items,pagination:{page,limit,total: result.total,totalPages: Math.ceil(result.total/limit)}})}catch(error){console.error('Users GET Error:',error)return Response.json({error: 'Failed to fetch users'},{status: 500})}}export async function POST(request: NextRequest){try{const body=await request.json()const result=await usersService.create(body)if(!result.success){return Response.json({error: result.error},{status: 400})}return Response.json({success: true,data: result.data},{status: 201})}catch(error){console.error('Users POST Error:',error)return Response.json({error: 'Failed to create users'},{status: 500})}}export async function PUT(request: NextRequest){try{const{searchParams}=new URL(request.url)const id=searchParams.get('id')if(!id){return Response.json({error: 'ID is required'},{status: 400})}const body=await request.json()const result=await usersService.update(id,body)if(!result.success){return Response.json({error: result.error},{status: 400})}return Response.json({success: true,data: result.data})}catch(error){console.error('Users PUT Error:',error)return Response.json({error: 'Failed to update users'},{status: 500})}}export async function DELETE(request: NextRequest){try{const{searchParams}=new URL(request.url)const id=searchParams.get('id')if(!id){return Response.json({error: 'ID is required'},{status: 400})}const result=await usersService.delete(id)if(!result.success){return Response.json({error: result.error},{status: 400})}return Response.json({success: true,message: 'Users deleted successfully'})}catch(error){console.error('Users DELETE Error:',error)return Response.json({error: 'Failed to delete users'},{status: 500})}}