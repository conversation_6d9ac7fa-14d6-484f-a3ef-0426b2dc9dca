export default function HomePage() { return ( React.createElement('div', null, {} <section className="relative py-20 px-4 min-h-screen flex items-center"> <div className="container mx-auto text-center relative z-10"> <div className="animate-kilat-fade-in"> {} <div className="mb-8"> <div className="inline-flex items-center justify-center w-24 h-24 mb-6 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 animate-kilat-glow"> <span className="text-4xl font-bold">⚡</span> ) </div> {} React.createElement('h1', null, Kilat.js ) {} React.createElement('p', null, The <span className="text-blue-400 font-semibold">lightning-fast</span> fullstack framework ) React.createElement('p', null, Built from scratch with <span className="text-purple-400">Bun.js</span>, <span className="text-cyan-400"> file-based routing</span>, and <span className="text-green-400"> zero dependencies</span> ) {} React.createElement('div', null, <button className="group relative px-8 py-4 bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl font-semibold text-lg transition-all duration-300 hover:scale-105 hover:shadow-2xl hover:shadow-blue-500/25 animate-kilat-scale-in"> <span className="relative z-10">Get Started</span> <div className="absolute inset-0 bg-gradient-to-r from-blue-600 to-purple-700 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300">) </button> React.createElement('button', null, <span className="flex items-center gap-2"> <span>View Documentation</span> <span className="group-hover:translate-x-1 transition-transform duration-300">→</span> </span> ) </div> {} React.createElement('div', null, <div className="bg-slate-800/50 backdrop-blur-sm border border-slate-700 rounded-2xl p-6 text-left"> <div className="flex items-center gap-2 mb-4"> <div className="w-3 h-3 bg-red-500 rounded-full">) React.createElement('div', null, ) React.createElement('div', null, ) React.createElement('span', null, Terminal) </div> React.createElement('pre', null, <span className="text-slate-500"># Create new Kilat.js app</span> <span className="text-blue-400">npm</span> create kilat-app my-app <span className="text-slate-500"># Start development server</span> <span className="text-purple-400">cd</span> my-app && <span className="text-cyan-400">bun</span> dev ) </div> </div> </div> </div> {} React.createElement('div', null, {} <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-blue-500/10 rounded-full blur-3xl animate-kilat-float">) React.createElement('div', null, ) React.createElement('div', null, ) {} React.createElement('div', null, ) </div> </section> {} React.createElement('section', null, <div className="container mx-auto"> <div className="text-center mb-20"> <h2 className="text-5xl md:text-6xl font-bold mb-6 bg-gradient-to-r from-white to-slate-300 bg-clip-text text-transparent animate-kilat-slide-up"> Why Choose Kilat.js? </h2> <p className="text-xl text-slate-400 max-w-3xl mx-auto animate-kilat-slide-up" style={{animationDelay: '0.2s'}}> Experience the next generation of fullstack development with unmatched performance and developer experience </p> </div> <div className="grid md:grid-cols-3 gap-8 max-w-6xl mx-auto"> {} <div className="group relative bg-gradient-to-br from-slate-800/50 to-slate-900/50 backdrop-blur-sm border border-slate-700 rounded-2xl p-8 transition-all duration-500 hover:scale-105 hover:border-blue-500/50 hover:shadow-2xl hover:shadow-blue-500/10 animate-kilat-slide-up" style={{animationDelay: '0.3s'}}> <div className="absolute inset-0 bg-gradient-to-br from-blue-500/5 to-transparent rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div> <div className="relative z-10"> <div className="w-16 h-16 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-2xl mb-6 flex items-center justify-center text-2xl animate-kilat-glow"> ⚡ </div> <h3 className="text-2xl font-bold mb-4 text-white group-hover:text-blue-400 transition-colors duration-300"> Lightning Fast </h3> <p className="text-slate-400 leading-relaxed group-hover:text-slate-300 transition-colors duration-300"> Built on Bun.js with native HTTP server. Zero dependencies on Express or Vite. Experience pure speed from the ground up with sub-millisecond response times. </p> <div className="mt-6 flex items-center text-sm text-blue-400"> <span>Learn more</span> <span className="ml-2 group-hover:translate-x-1 transition-transform duration-300">→</span> </div> </div> </div> {} <div className="group relative bg-gradient-to-br from-slate-800/50 to-slate-900/50 backdrop-blur-sm border border-slate-700 rounded-2xl p-8 transition-all duration-500 hover:scale-105 hover:border-purple-500/50 hover:shadow-2xl hover:shadow-purple-500/10 animate-kilat-slide-up" style={{animationDelay: '0.4s'}}> <div className="absolute inset-0 bg-gradient-to-br from-purple-500/5 to-transparent rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div> <div className="relative z-10"> <div className="w-16 h-16 bg-gradient-to-r from-purple-500 to-pink-500 rounded-2xl mb-6 flex items-center justify-center text-2xl animate-kilat-glow" style={{animationDelay: '0.5s'}}> 🏗️ </div> <h3 className="text-2xl font-bold mb-4 text-white group-hover:text-purple-400 transition-colors duration-300"> Complete Framework </h3> <p className="text-slate-400 leading-relaxed group-hover:text-slate-300 transition-colors duration-300"> Everything you need in one package: routing, state management, ORM, build tools, and deployment. No more dependency hell or configuration nightmares. </p> <div className="mt-6 flex items-center text-sm text-purple-400"> <span>Explore features</span> <span className="ml-2 group-hover:translate-x-1 transition-transform duration-300">→</span> </div> </div> </div> {} <div className="group relative bg-gradient-to-br from-slate-800/50 to-slate-900/50 backdrop-blur-sm border border-slate-700 rounded-2xl p-8 transition-all duration-500 hover:scale-105 hover:border-green-500/50 hover:shadow-2xl hover:shadow-green-500/10 animate-kilat-slide-up" style={{animationDelay: '0.5s'}}> <div className="absolute inset-0 bg-gradient-to-br from-green-500/5 to-transparent rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div> <div className="relative z-10"> <div className="w-16 h-16 bg-gradient-to-r from-green-500 to-emerald-500 rounded-2xl mb-6 flex items-center justify-center text-2xl animate-kilat-glow" style={{animationDelay: '1s'}}> 🎨 </div> <h3 className="text-2xl font-bold mb-4 text-white group-hover:text-green-400 transition-colors duration-300"> Amazing DX </h3> <p className="text-slate-400 leading-relaxed group-hover:text-slate-300 transition-colors duration-300"> File-based routing, instant hot reload, built-in themes, powerful CLI generators, and TypeScript-first development. Joy in every keystroke. </p> <div className="mt-6 flex items-center text-sm text-green-400"> <span>See examples</span> <span className="ml-2 group-hover:translate-x-1 transition-transform duration-300">→</span> </div> </div> </div> </div> </div> ) {} React.createElement('section', null, <div className="container mx-auto"> <div className="text-center mb-20"> <h2 className="text-5xl md:text-6xl font-bold mb-6 bg-gradient-to-r from-white to-slate-300 bg-clip-text text-transparent animate-kilat-slide-up"> Performance That Speaks </h2> <p className="text-xl text-slate-400 max-w-3xl mx-auto animate-kilat-slide-up" style={{animationDelay: '0.2s'}}> Numbers don't lie. See how Kilat.js outperforms traditional frameworks </p> </div> <div className="grid md:grid-cols-4 gap-8 max-w-5xl mx-auto"> <div className="text-center animate-kilat-scale-in" style={{animationDelay: '0.3s'}}> <div className="text-5xl md:text-6xl font-black bg-gradient-to-r from-blue-400 to-cyan-400 bg-clip-text text-transparent mb-4 animate-kilat-pulse"> &lt;10ms </div> <p className="text-slate-300 font-semibold mb-2">Cold Start</p> <p className="text-slate-500 text-sm">Lightning fast server startup</p> </div> <div className="text-center animate-kilat-scale-in" style={{animationDelay: '0.4s'}}> <div className="text-5xl md:text-6xl font-black bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent mb-4 animate-kilat-pulse" style={{animationDelay: '0.5s'}}> 0.1s </div> <p className="text-slate-300 font-semibold mb-2">Hot Reload</p> <p className="text-slate-500 text-sm">Instant development feedback</p> </div> <div className="text-center animate-kilat-scale-in" style={{animationDelay: '0.5s'}}> <div className="text-5xl md:text-6xl font-black bg-gradient-to-r from-green-400 to-emerald-400 bg-clip-text text-transparent mb-4 animate-kilat-pulse" style={{animationDelay: '1s'}}> 50KB </div> <p className="text-slate-300 font-semibold mb-2">Bundle Size</p> <p className="text-slate-500 text-sm">Minimal production footprint</p> </div> <div className="text-center animate-kilat-scale-in" style={{animationDelay: '0.6s'}}> <div className="text-5xl md:text-6xl font-black bg-gradient-to-r from-orange-400 to-red-400 bg-clip-text text-transparent mb-4 animate-kilat-pulse" style={{animationDelay: '1.5s'}}> 100% </div> <p className="text-slate-300 font-semibold mb-2">TypeScript</p> <p className="text-slate-500 text-sm">Full type safety out of the box</p> </div> </div> </div> ) {} React.createElement('section', null, <div className="container mx-auto"> <div className="text-center mb-20"> <h2 className="text-5xl md:text-6xl font-bold mb-6 bg-gradient-to-r from-white to-slate-300 bg-clip-text text-transparent animate-kilat-slide-up"> See It In Action </h2> <p className="text-xl text-slate-400 max-w-3xl mx-auto animate-kilat-slide-up" style={{animationDelay: '0.2s'}}> From zero to production in minutes. Experience the simplicity of Kilat.js </p> </div> <div className="max-w-4xl mx-auto"> <div className="bg-slate-900/80 backdrop-blur-sm border border-slate-700 rounded-3xl overflow-hidden animate-kilat-scale-in" style={{animationDelay: '0.4s'}}> {} <div className="flex items-center gap-2 px-6 py-4 bg-slate-800/50 border-b border-slate-700"> <div className="w-3 h-3 bg-red-500 rounded-full"></div> <div className="w-3 h-3 bg-yellow-500 rounded-full"></div> <div className="w-3 h-3 bg-green-500 rounded-full"></div> <span className="text-slate-400 text-sm ml-4">~/my-kilat-app</span> </div> {} <div className="p-8"> <div className="space-y-6"> {} <div className="animate-kilat-fade-in" style={{animationDelay: '0.6s'}}> <div className="text-slate-500 text-sm mb-2"># Create a new Kilat.js application</div> <div className="font-mono text-lg"> <span className="text-blue-400">npm</span> <span className="text-white">create</span> <span className="text-green-400">kilat-app</span> <span className="text-purple-400">my-app</span> </div> </div> {} <div className="animate-kilat-fade-in" style={{animationDelay: '0.8s'}}> <div className="text-slate-500 text-sm mb-2"># Navigate and start development</div> <div className="font-mono text-lg"> <span className="text-cyan-400">cd</span> <span className="text-purple-400">my-app</span> <span className="text-slate-500">&&</span> <span className="text-orange-400">bun</span> <span className="text-green-400">dev</span> </div> </div> {} <div className="animate-kilat-fade-in" style={{animationDelay: '1s'}}> <div className="bg-slate-800/50 rounded-xl p-4 border border-slate-600"> <div className="text-green-400 font-mono text-sm space-y-1"> <div>⚡ Kilat.js v1.0.0</div> <div>🚀 SpeedRun server running on http: <div>🔥 Hot Module Replacement enabled</div> <div>✅ Ready in 47ms</div> </div> </div> </div> </div> </div> </div> <div className="text-center mt-12 animate-kilat-slide-up" style={{animationDelay: '1.2s'}}> <p className="text-slate-400 mb-8"> That's it! Your Kilat.js application is ready for development. </p> <button className="group px-8 py-4 bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl font-semibold text-lg transition-all duration-300 hover:scale-105 hover:shadow-2xl hover:shadow-blue-500/25"> <span className="flex items-center gap-2"> <span>Try It Now</span> <span className="group-hover:translate-x-1 transition-transform duration-300">🚀</span> </span> </button> </div> </div> </div> ) {} React.createElement('footer', null, <div className="container mx-auto text-center"> <div className="mb-8"> <div className="inline-flex items-center justify-center w-16 h-16 mb-4 rounded-full bg-gradient-to-r from-blue-500 to-purple-600"> <span className="text-2xl font-bold">⚡</span> </div> <h3 className="text-2xl font-bold text-white mb-2">Kilat.js</h3> <p className="text-slate-400">Lightning-fast fullstack framework</p> </div> <div className="flex flex-wrap justify-center gap-8 mb-8 text-slate-400"> <a href="#" className="hover:text-blue-400 transition-colors duration-300">Documentation</a> <a href="#" className="hover:text-blue-400 transition-colors duration-300">Examples</a> <a href="#" className="hover:text-blue-400 transition-colors duration-300">GitHub</a> <a href="#" className="hover:text-blue-400 transition-colors duration-300">Community</a> <a href="#" className="hover:text-blue-400 transition-colors duration-300">Blog</a> </div> <div className="text-slate-500 text-sm"> <p>&copy; 2024 Kilat.js. Built with ⚡ and ❤️</p> </div> </div> ) </div> ) }
//# sourceMappingURL=apps\page.tsx.map