/**
 * Theme Manager - Manages theme switching and CSS generation
 * Handles dynamic theme loading and CSS variable injection
 */

import { glowTheme } from './themes/glow'
import { cyberTheme } from './themes/cyber'
import { pastelTheme } from './themes/pastel'
import { retroTheme } from './themes/retro'

export interface Theme {
  name: string
  displayName: string
  type: 'light' | 'dark'
  colors: any
  cssVars: Record<string, string>
  shadows: Record<string, string>
  gradients: Record<string, string>
  typography: any
  animations: Record<string, string>
  components: any
  breakpoints?: Record<string, string>
}

export class ThemeManager {
  private themes: Map<string, Theme> = new Map()
  private currentTheme: string = 'glow'
  private isClient: boolean = typeof window !== 'undefined'

  constructor() {
    this.registerBuiltinThemes()
  }

  /**
   * Register built-in themes
   */
  private registerBuiltinThemes(): void {
    this.registerTheme(glowTheme as Theme)
    this.registerTheme(cyberTheme as Theme)
    this.registerTheme(pastelTheme as Theme)
    this.registerTheme(retroTheme as Theme)
  }

  /**
   * Register a new theme
   */
  registerTheme(theme: Theme): void {
    this.themes.set(theme.name, theme)
  }

  /**
   * Get theme by name
   */
  getTheme(name: string): Theme | undefined {
    return this.themes.get(name)
  }

  /**
   * Get all available themes
   */
  getAllThemes(): Theme[] {
    return Array.from(this.themes.values())
  }

  /**
   * Get current theme
   */
  getCurrentTheme(): Theme | undefined {
    return this.themes.get(this.currentTheme)
  }

  /**
   * Set current theme
   */
  setTheme(themeName: string): void {
    const theme = this.themes.get(themeName)
    if (!theme) {
      console.warn(`Theme "${themeName}" not found`)
      return
    }

    this.currentTheme = themeName
    
    if (this.isClient) {
      this.applyThemeToDOM(theme)
      this.saveThemePreference(themeName)
    }
  }

  /**
   * Apply theme to DOM
   */
  private applyThemeToDOM(theme: Theme): void {
    const root = document.documentElement

    // Remove existing theme classes
    this.getAllThemes().forEach(t => {
      root.classList.remove(`kilat-theme-${t.name}`)
    })

    // Add new theme class
    root.classList.add(`kilat-theme-${theme.name}`)

    // Apply CSS variables
    Object.entries(theme.cssVars).forEach(([property, value]) => {
      root.style.setProperty(property, value)
    })

    // Dispatch theme change event
    window.dispatchEvent(new CustomEvent('kilat:theme-changed', {
      detail: { theme: theme.name }
    }))
  }

  /**
   * Save theme preference to localStorage
   */
  private saveThemePreference(themeName: string): void {
    try {
      localStorage.setItem('kilat-theme', themeName)
    } catch (error) {
      console.warn('Failed to save theme preference:', error)
    }
  }

  /**
   * Load theme preference from localStorage
   */
  loadThemePreference(): string {
    try {
      return localStorage.getItem('kilat-theme') || 'glow'
    } catch (error) {
      console.warn('Failed to load theme preference:', error)
      return 'glow'
    }
  }

  /**
   * Initialize theme system
   */
  initialize(defaultTheme: string = 'glow'): void {
    if (this.isClient) {
      // Load saved preference or use default
      const savedTheme = this.loadThemePreference()
      const themeToUse = this.themes.has(savedTheme) ? savedTheme : defaultTheme
      
      this.setTheme(themeToUse)
    } else {
      // Server-side: just set the current theme
      this.currentTheme = defaultTheme
    }
  }

  /**
   * Generate CSS for all themes
   */
  generateThemeCSS(): string {
    let css = ''

    for (const theme of this.themes.values()) {
      css += this.generateThemeVariables(theme)
    }

    return css
  }

  /**
   * Generate CSS variables for a theme
   */
  private generateThemeVariables(theme: Theme): string {
    let css = `.kilat-theme-${theme.name} {\n`

    // Add CSS variables
    Object.entries(theme.cssVars).forEach(([property, value]) => {
      css += `  ${property}: ${value};\n`
    })

    css += '}\n\n'

    return css
  }

  /**
   * Get theme colors for Tailwind config
   */
  getThemeColorsForTailwind(themeName: string): any {
    const theme = this.themes.get(themeName)
    if (!theme) return {}

    return {
      primary: theme.colors.primary,
      secondary: theme.colors.secondary,
      accent: theme.colors.accent,
      neutral: theme.colors.neutral,
      success: theme.colors.success,
      warning: theme.colors.warning,
      error: theme.colors.error,
    }
  }

  /**
   * Toggle between light and dark themes
   */
  toggleThemeType(): void {
    const currentTheme = this.getCurrentTheme()
    if (!currentTheme) return

    // Find a theme of the opposite type
    const oppositeType = currentTheme.type === 'dark' ? 'light' : 'dark'
    const oppositeTheme = Array.from(this.themes.values())
      .find(theme => theme.type === oppositeType)

    if (oppositeTheme) {
      this.setTheme(oppositeTheme.name)
    }
  }

  /**
   * Get theme preview data
   */
  getThemePreview(themeName: string): any {
    const theme = this.themes.get(themeName)
    if (!theme) return null

    return {
      name: theme.name,
      displayName: theme.displayName,
      type: theme.type,
      primaryColor: theme.cssVars['--kilat-primary'],
      secondaryColor: theme.cssVars['--kilat-secondary'],
      backgroundColor: theme.cssVars['--kilat-background'],
      textColor: theme.cssVars['--kilat-text'],
    }
  }

  /**
   * Create custom theme
   */
  createCustomTheme(config: Partial<Theme>): Theme {
    const baseTheme = this.themes.get('glow')!
    
    const customTheme: Theme = {
      ...baseTheme,
      ...config,
      name: config.name || 'custom',
      displayName: config.displayName || 'Custom',
    }

    this.registerTheme(customTheme)
    return customTheme
  }
}

// Export singleton instance
export const themeManager = new ThemeManager()

// Export theme utilities
export const useTheme = () => {
  return {
    currentTheme: themeManager.getCurrentTheme(),
    setTheme: (name: string) => themeManager.setTheme(name),
    getAllThemes: () => themeManager.getAllThemes(),
    toggleThemeType: () => themeManager.toggleThemeType(),
  }
}
