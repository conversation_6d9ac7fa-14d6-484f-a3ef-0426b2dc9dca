/**
 * KilatCSS Configuration
 * Tailwind CSS configuration with Kilat.js theme integration
 */

/** @type {import('tailwindcss').Config} */
export default {
  content: [
    './apps/**/*.{js,ts,jsx,tsx}',
    './components/**/*.{js,ts,jsx,tsx}',
    './core/**/*.{js,ts,jsx,tsx}',
  ],

  darkMode: ['class', '.kilat-theme-glow', '.kilat-theme-cyber', '.kilat-theme-retro'],

  theme: {
    extend: {
      // 🎨 Theme-aware colors using CSS variables
      colors: {
        // Dynamic theme colors
        primary: {
          DEFAULT: 'var(--kilat-primary)',
          50: 'rgba(var(--kilat-primary-rgb), 0.05)',
          100: 'rgba(var(--kilat-primary-rgb), 0.1)',
          200: 'rgba(var(--kilat-primary-rgb), 0.2)',
          300: 'rgba(var(--kilat-primary-rgb), 0.3)',
          400: 'rgba(var(--kilat-primary-rgb), 0.4)',
          500: 'var(--kilat-primary)',
          600: 'rgba(var(--kilat-primary-rgb), 0.8)',
          700: 'rgba(var(--kilat-primary-rgb), 0.9)',
          800: 'rgba(var(--kilat-primary-rgb), 0.95)',
          900: 'rgba(var(--kilat-primary-rgb), 0.98)',
        },
        secondary: {
          DEFAULT: 'var(--kilat-secondary)',
          50: 'rgba(var(--kilat-secondary-rgb), 0.05)',
          100: 'rgba(var(--kilat-secondary-rgb), 0.1)',
          200: 'rgba(var(--kilat-secondary-rgb), 0.2)',
          300: 'rgba(var(--kilat-secondary-rgb), 0.3)',
          400: 'rgba(var(--kilat-secondary-rgb), 0.4)',
          500: 'var(--kilat-secondary)',
          600: 'rgba(var(--kilat-secondary-rgb), 0.8)',
          700: 'rgba(var(--kilat-secondary-rgb), 0.9)',
          800: 'rgba(var(--kilat-secondary-rgb), 0.95)',
          900: 'rgba(var(--kilat-secondary-rgb), 0.98)',
        },
        accent: {
          DEFAULT: 'var(--kilat-accent)',
          50: 'rgba(var(--kilat-accent-rgb), 0.05)',
          100: 'rgba(var(--kilat-accent-rgb), 0.1)',
          200: 'rgba(var(--kilat-accent-rgb), 0.2)',
          300: 'rgba(var(--kilat-accent-rgb), 0.3)',
          400: 'rgba(var(--kilat-accent-rgb), 0.4)',
          500: 'var(--kilat-accent)',
          600: 'rgba(var(--kilat-accent-rgb), 0.8)',
          700: 'rgba(var(--kilat-accent-rgb), 0.9)',
          800: 'rgba(var(--kilat-accent-rgb), 0.95)',
          900: 'rgba(var(--kilat-accent-rgb), 0.98)',
        },

        // Semantic colors
        background: 'var(--kilat-background)',
        surface: 'var(--kilat-surface)',
        text: 'var(--kilat-text)',
        muted: 'var(--kilat-muted)',
        border: 'var(--kilat-border)',
        success: 'var(--kilat-success)',
        warning: 'var(--kilat-warning)',
        error: 'var(--kilat-error)',

        // Static theme colors for fallback
        'glow-primary': '#3b82f6',
        'glow-secondary': '#8b5cf6',
        'glow-accent': '#06b6d4',
        'cyber-primary': '#00ff88',
        'cyber-secondary': '#ff0080',
        'cyber-accent': '#00d4ff',
        'pastel-primary': '#0ea5e9',
        'pastel-secondary': '#ec4899',
        'pastel-accent': '#22c55e',
        'retro-primary': '#f59e0b',
        'retro-secondary': '#ef4444',
        'retro-accent': '#10b981',
      },

      // 🌟 Enhanced Kilat Animations
      animation: {
        'kilat-fade-in': 'kilatFadeIn 0.8s ease-out',
        'kilat-slide-up': 'kilatSlideUp 0.8s ease-out',
        'kilat-slide-down': 'kilatSlideDown 0.8s ease-out',
        'kilat-scale-in': 'kilatScaleIn 0.6s ease-out',
        'kilat-bounce-in': 'kilatBounceIn 0.8s ease-out',
        'kilat-glow': 'kilatGlow 3s ease-in-out infinite alternate',
        'kilat-pulse': 'kilatPulse 2s ease-in-out infinite',
        'kilat-spin-slow': 'spin 3s linear infinite',
        'kilat-float': 'kilatFloat 6s ease-in-out infinite',
        'kilat-gradient': 'kilatGradient 4s ease-in-out infinite',
        'neon-pulse': 'neonPulse 2s ease-in-out infinite alternate',
        'retro-blink': 'retroBlink 1s step-end infinite',
        'soft-bounce': 'softBounce 2s ease-in-out infinite',
      },
      
      keyframes: {
        kilatFadeIn: {
          'from': { opacity: '0', transform: 'translateY(10px)' },
          'to': { opacity: '1', transform: 'translateY(0)' },
        },
        kilatSlideUp: {
          'from': { transform: 'translateY(30px)', opacity: '0' },
          'to': { transform: 'translateY(0)', opacity: '1' },
        },
        kilatSlideDown: {
          'from': { transform: 'translateY(-30px)', opacity: '0' },
          'to': { transform: 'translateY(0)', opacity: '1' },
        },
        kilatScaleIn: {
          'from': { transform: 'scale(0.8)', opacity: '0' },
          'to': { transform: 'scale(1)', opacity: '1' },
        },
        kilatBounceIn: {
          '0%': { transform: 'scale(0.3)', opacity: '0' },
          '50%': { transform: 'scale(1.05)' },
          '70%': { transform: 'scale(0.9)' },
          '100%': { transform: 'scale(1)', opacity: '1' },
        },
        kilatGlow: {
          'from': { boxShadow: '0 0 10px var(--kilat-primary)' },
          'to': { boxShadow: '0 0 30px var(--kilat-primary), 0 0 60px var(--kilat-primary)' },
        },
        kilatPulse: {
          '0%, 100%': { transform: 'scale(1)', opacity: '1' },
          '50%': { transform: 'scale(1.05)', opacity: '0.8' },
        },
        kilatFloat: {
          '0%, 100%': { transform: 'translateY(0px)' },
          '50%': { transform: 'translateY(-20px)' },
        },
        kilatGradient: {
          '0%': { backgroundPosition: '0% 50%' },
          '50%': { backgroundPosition: '100% 50%' },
          '100%': { backgroundPosition: '0% 50%' },
        },
        neonPulse: {
          'from': { textShadow: '0 0 10px currentColor' },
          'to': { textShadow: '0 0 20px currentColor, 0 0 30px currentColor' },
        },
        retroBlink: {
          '0%, 50%': { opacity: '1' },
          '51%, 100%': { opacity: '0' },
        },
        softBounce: {
          '0%, 100%': { transform: 'translateY(0)' },
          '50%': { transform: 'translateY(-5px)' },
        },
      },

      // 🔤 Enhanced Typography
      fontFamily: {
        sans: ['Inter', 'system-ui', 'sans-serif'],
        mono: ['JetBrains Mono', 'Consolas', 'monospace'],
        display: ['Poppins', 'system-ui', 'sans-serif'],
        orbitron: ['Orbitron', 'system-ui', 'sans-serif'],
        courier: ['Courier New', 'monospace'],
      },

      // 📐 Enhanced Spacing & Sizing
      spacing: {
        '18': '4.5rem',
        '88': '22rem',
        '128': '32rem',
      },

      // 🎭 Enhanced Effects
      backdropBlur: {
        'kilat': '12px',
      },

      boxShadow: {
        'glow': '0 0 20px rgba(59, 130, 246, 0.5)',
        'glow-lg': '0 0 40px rgba(59, 130, 246, 0.6)',
        'glow-xl': '0 0 60px rgba(59, 130, 246, 0.7)',
        'cyber': '0 0 20px rgba(0, 255, 136, 0.8)',
        'cyber-lg': '0 0 40px rgba(0, 255, 136, 0.9)',
        'neon': '0 0 5px currentColor, 0 0 10px currentColor, 0 0 15px currentColor',
        'retro': '4px 4px 0px rgba(245, 158, 11, 0.8)',
        'retro-lg': '8px 8px 0px rgba(245, 158, 11, 0.8)',
        'soft': '0 4px 20px rgba(0, 0, 0, 0.08)',
        'soft-lg': '0 8px 40px rgba(0, 0, 0, 0.12)',
      },

      // 🌈 Background gradients
      backgroundImage: {
        'gradient-primary': 'linear-gradient(45deg, var(--kilat-primary), var(--kilat-secondary))',
        'gradient-secondary': 'linear-gradient(45deg, var(--kilat-secondary), var(--kilat-accent))',
        'gradient-accent': 'linear-gradient(45deg, var(--kilat-accent), var(--kilat-primary))',
        'gradient-glow': 'linear-gradient(135deg, #0a0f1c 0%, #1e293b 50%, #334155 100%)',
        'gradient-cyber': 'linear-gradient(90deg, #00ff88, #00d4ff, #ff0080, #00ff88)',
        'gradient-retro': 'linear-gradient(90deg, #f59e0b, #ef4444, #dc2626)',
      },
    },
  },
  
  plugins: [
    // Enhanced Kilat.js plugin
    function({ addUtilities, addComponents, theme }) {
      // Add utility classes
      addUtilities({
        '.text-balance': {
          'text-wrap': 'balance',
        },
        '.glass': {
          'background': 'rgba(255, 255, 255, 0.05)',
          'backdrop-filter': 'blur(10px)',
          'border': '1px solid rgba(255, 255, 255, 0.1)',
        },
        '.glass-dark': {
          'background': 'rgba(0, 0, 0, 0.2)',
          'backdrop-filter': 'blur(10px)',
          'border': '1px solid rgba(255, 255, 255, 0.1)',
        },
        '.gradient-text': {
          'background': 'linear-gradient(45deg, var(--kilat-primary), var(--kilat-secondary))',
          'background-size': '300% 300%',
          '-webkit-background-clip': 'text',
          '-webkit-text-fill-color': 'transparent',
          'background-clip': 'text',
          'animation': 'kilatGradient 4s ease-in-out infinite',
        },
      })

      // Add component classes
      addComponents({
        '.kilat-container': {
          '@apply mx-auto max-w-7xl px-4 sm:px-6 lg:px-8': {},
        },
        '.kilat-card': {
          '@apply rounded-lg border border-border bg-surface text-text shadow-sm': {},
        },
        '.kilat-button': {
          '@apply inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none': {},
        },
        '.kilat-input': {
          '@apply flex h-10 w-full rounded-md border border-border bg-transparent px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50': {},
        },
      })
    },
  ],
}
